/* Main container */
.ludo-container {
  text-align: center;
  padding: 20px;
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.ludo-container h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* Game setup */
.game-setup {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  max-width: 400px;
  margin: 0 auto;
}

.game-setup h2 {
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.player-selection {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 20px;
}

.player-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.player-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.player-btn.selected {
  background: #4CAF50;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.start-btn {
  padding: 15px 30px;
  font-size: 1.2rem;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.start-btn:hover {
  background: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

/* Game info */
.game-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 600px;
  margin: 0 auto 20px;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.current-player {
  font-size: 1.2rem;
  font-weight: bold;
}

.player-name {
  padding: 5px 10px;
  border-radius: 5px;
  text-transform: uppercase;
}

.player-name.red { background: #f44336; }
.player-name.green { background: #4CAF50; }
.player-name.yellow { background: #FFC107; color: black; }
.player-name.blue { background: #2196F3; }

/* Dice section */
.dice-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.dice {
  width: 60px;
  height: 60px;
  background: white;
  color: black;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  transition: transform 0.1s ease;
}

.dice.rolling {
  animation: diceRoll 0.1s infinite;
}

@keyframes diceRoll {
  0% { transform: rotate(0deg) scale(1); }
  25% { transform: rotate(90deg) scale(1.1); }
  50% { transform: rotate(180deg) scale(1); }
  75% { transform: rotate(270deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

.dice-btn {
  padding: 10px 20px;
  background: #FF9800;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.dice-btn:hover:not(:disabled) {
  background: #F57C00;
  transform: translateY(-2px);
}

.dice-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Board container */
.board-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.board {
  display: grid;
  grid-template-columns: repeat(15, 40px);
  grid-template-rows: repeat(15, 40px);
  gap: 1px;
  background: #333;
  padding: 10px;
  border-radius: 15px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.3);
  position: relative;
}

/* Board cells */
.cell {
  width: 40px;
  height: 40px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  position: relative;
}

.cell.path {
  background: #fff;
  border: 2px solid #333;
}

.cell.safe {
  background: #FFD700;
  border: 2px solid #FFA000;
  position: relative;
}

.cell.safe::after {
  content: '★';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #FF6F00;
  font-size: 1.2rem;
}

.cell.home {
  border: 3px solid;
  border-radius: 8px;
}

.cell.home.red {
  background: #ffebee;
  border-color: #f44336;
}

.cell.home.green {
  background: #e8f5e8;
  border-color: #4CAF50;
}

.cell.home.yellow {
  background: #fffde7;
  border-color: #FFC107;
}

.cell.home.blue {
  background: #e3f2fd;
  border-color: #2196F3;
}

.cell.home-stretch {
  border: 2px solid;
  position: relative;
}

.cell.home-stretch.red {
  background: #ffcdd2;
  border-color: #f44336;
}

.cell.home-stretch.green {
  background: #c8e6c9;
  border-color: #4CAF50;
}

.cell.home-stretch.yellow {
  background: #fff9c4;
  border-color: #FFC107;
}

.cell.home-stretch.blue {
  background: #bbdefb;
  border-color: #2196F3;
}

.cell.home-stretch::after {
  content: '→';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1rem;
  font-weight: bold;
}

.cell.start {
  background: #E8F5E8;
  border: 3px solid #4CAF50;
  position: relative;
}

.cell.start::after {
  content: '🏠';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1rem;
}

/* Game pieces */
.piece {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 3px solid #333;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.piece.red {
  background: #f44336;
  border-color: #d32f2f;
}

.piece.green {
  background: #4CAF50;
  border-color: #388e3c;
}

.piece.yellow {
  background: #FFC107;
  border-color: #f57f17;
}

.piece.blue {
  background: #2196F3;
  border-color: #1976d2;
}

.piece.movable {
  cursor: pointer;
  animation: pulse 1s infinite;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
}

.piece.animating {
  animation: moveAnimation 0.5s ease-in-out;
}

@keyframes pulse {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.1); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

@keyframes moveAnimation {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

.piece:hover.movable {
  transform: translate(-50%, -50%) scale(1.15);
  box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}

/* Winner announcement */
.winner-announcement {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  padding: 30px;
  border-radius: 15px;
  margin: 20px auto;
  max-width: 400px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.3);
  animation: celebrationBounce 0.6s ease-out;
}

@keyframes celebrationBounce {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

.winner-announcement h2 {
  margin-bottom: 20px;
  font-size: 2rem;
  color: #4CAF50;
}

.winner-announcement button {
  padding: 12px 24px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.winner-announcement button:hover {
  background: #45a049;
  transform: translateY(-2px);
}

/* Game controls */
.game-controls {
  margin-top: 20px;
}

.game-controls button {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-controls button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
  .board {
    grid-template-columns: repeat(15, 30px);
    grid-template-rows: repeat(15, 30px);
  }

  .cell {
    width: 30px;
    height: 30px;
  }

  .piece {
    width: 22px;
    height: 22px;
  }

  .game-info {
    flex-direction: column;
    gap: 15px;
  }

  .ludo-container h1 {
    font-size: 2rem;
  }
}
  