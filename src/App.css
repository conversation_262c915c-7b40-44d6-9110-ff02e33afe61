* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  width: 100vw;
  height: 100vh;
}

body, #root, .App {
  width: 100%;
  height: 100%;
}

/* Home */
#home {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

#game-list {
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.game-card {
  display: inline-block;
  padding: 10px 20px;
  margin: 10px;
  border-radius: 5px;
  border: 1px solid white;
  border-radius: 10px;
  text-decoration: none;
  cursor: pointer;
  color: white;
}