import { useState, useEffect } from 'react'
import './Ludo.css'

// Ludo board path - represents the movement path for pieces
const BOARD_PATH = [
  // Red starting path (bottom) - positions 0-12
  [13, 6], [12, 6], [11, 6], [10, 6], [9, 6], [8, 6],
  [7, 6], [6, 6], [5, 6], [4, 6], [3, 6], [2, 6], [1, 6],
  // Green starting path (left) - positions 13-25
  [0, 6], [0, 7], [0, 8], [1, 8], [2, 8], [3, 8], [4, 8], [5, 8], [6, 8],
  [7, 8], [8, 8], [9, 8], [10, 8], [11, 8], [12, 8], [13, 8],
  // Yellow starting path (top) - positions 26-38
  [14, 8], [14, 7], [14, 6], [14, 5], [13, 5], [12, 5], [11, 5], [10, 5], [9, 5], [8, 5],
  [7, 5], [6, 5], [5, 5], [4, 5], [3, 5], [2, 5], [1, 5],
  // <PERSON> starting path (right) - positions 39-51
  [0, 5], [0, 4], [0, 3], [1, 3], [2, 3], [3, 3], [4, 3], [5, 3], [6, 3],
  [7, 3], [8, 3], [9, 3], [10, 3], [11, 3], [12, 3], [13, 3], [14, 3], [14, 4], [14, 5]
]

// Home stretch paths (final 5 squares before winning)
const HOME_STRETCH_PATHS = {
  red: [[12, 7], [11, 7], [10, 7], [9, 7], [8, 7]], // positions 52-56
  green: [[1, 7], [2, 7], [3, 7], [4, 7], [5, 7]], // positions 52-56
  yellow: [[13, 4], [12, 4], [11, 4], [10, 4], [9, 4]], // positions 52-56
  blue: [[1, 4], [2, 4], [3, 4], [4, 4], [5, 4]] // positions 52-56
}

// Home positions for each color
const HOME_POSITIONS = {
  red: [[11, 2], [11, 3], [12, 2], [12, 3]],
  green: [[2, 11], [2, 12], [3, 11], [3, 12]],
  yellow: [[2, 2], [2, 3], [3, 2], [3, 3]],
  blue: [[11, 11], [11, 12], [12, 11], [12, 12]]
}

// Starting positions on the main path
const START_POSITIONS = {
  red: 0,
  green: 13,
  yellow: 26,
  blue: 39
}

// Safe positions (star positions)
const SAFE_POSITIONS = [0, 8, 13, 21, 26, 34, 39, 47]

function Ludo() {
  const [gameStarted, setGameStarted] = useState(false)
  const [numPlayers, setNumPlayers] = useState(2)
  const [activePlayers, setActivePlayers] = useState(['red', 'green'])
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0)
  const [diceValue, setDiceValue] = useState(null)
  const [isRolling, setIsRolling] = useState(false)
  const [gameState, setGameState] = useState({
    red: { pieces: [-1, -1, -1, -1], finished: 0 },
    green: { pieces: [-1, -1, -1, -1], finished: 0 },
    yellow: { pieces: [-1, -1, -1, -1], finished: 0 },
    blue: { pieces: [-1, -1, -1, -1], finished: 0 }
  })

  const [movablePieces, setMovablePieces] = useState([])
  const [winner, setWinner] = useState(null)
  const [animatingPiece, setAnimatingPiece] = useState(null)

  const currentPlayer = activePlayers[currentPlayerIndex]

  // Initialize game when players are selected
  useEffect(() => {
    if (gameStarted) {
      const players = ['red', 'green', 'yellow', 'blue'].slice(0, numPlayers)
      setActivePlayers(players)
      setCurrentPlayerIndex(0)
    }
  }, [gameStarted, numPlayers])

  // Check for movable pieces after dice roll
  useEffect(() => {
    if (diceValue && !winner) {
      const movable = getMovablePieces(currentPlayer, diceValue)
      setMovablePieces(movable)

      // Auto-move if only one piece can move
      if (movable.length === 1) {
        setTimeout(() => {
          movePiece(currentPlayer, movable[0])
        }, 500)
      } else if (movable.length === 0) {
        // No moves available, switch to next player
        setTimeout(() => {
          nextPlayer()
        }, 1000)
      }
    }
  }, [diceValue, currentPlayer, gameState])

  const startGame = () => {
    setGameStarted(true)
    setGameState({
      red: { pieces: [-1, -1, -1, -1], finished: 0 },
      green: { pieces: [-1, -1, -1, -1], finished: 0 },
      yellow: { pieces: [-1, -1, -1, -1], finished: 0 },
      blue: { pieces: [-1, -1, -1, -1], finished: 0 }
    })
    setWinner(null)
    setDiceValue(null)
  }

  const rollDice = async () => {
    if (isRolling || winner) return

    setIsRolling(true)
    setMovablePieces([])

    // Animate dice rolling
    for (let i = 0; i < 10; i++) {
      setDiceValue(Math.floor(Math.random() * 6) + 1)
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    const finalRoll = Math.floor(Math.random() * 6) + 1
    setDiceValue(finalRoll)
    setIsRolling(false)
  }

  const getMovablePieces = (player, diceRoll) => {
    const playerState = gameState[player]
    const movable = []

    playerState.pieces.forEach((position, index) => {
      if (position === -1) {
        // Piece is in home, can only move out with 6
        if (diceRoll === 6) {
          movable.push(index)
        }
      } else if (position === 56) {
        // Piece has finished, can't move
        return
      } else if (position >= 52) {
        // Piece is in home stretch
        const newPosition = position + diceRoll
        if (newPosition <= 56) {
          movable.push(index)
        }
      } else {
        // Piece is on main board
        const playerStartPos = START_POSITIONS[player]
        const distanceFromStart = (position - playerStartPos + 52) % 52
        const totalDistance = distanceFromStart + diceRoll

        if (totalDistance <= 56) { // Can move normally or enter home stretch
          movable.push(index)
        }
      }
    })

    return movable
  }

  const movePiece = async (player, pieceIndex) => {
    if (!diceValue) return

    const newGameState = { ...gameState }
    const currentPosition = newGameState[player].pieces[pieceIndex]

    setAnimatingPiece({ player, piece: pieceIndex })

    let newPosition
    if (currentPosition === -1) {
      // Moving out of home
      newPosition = START_POSITIONS[player]
    } else {
      // Calculate new position
      let targetPosition = currentPosition + diceValue

      // Handle entering home stretch
      if (currentPosition < 52 && targetPosition >= 52) {
        // Entering home stretch - need to calculate correctly for each player
        const playerStartPos = START_POSITIONS[player]
        const distanceFromStart = (currentPosition - playerStartPos + 52) % 52
        const totalDistance = distanceFromStart + diceValue

        if (totalDistance >= 51) {
          // Entering home stretch
          const homeStretchSteps = totalDistance - 51
          if (homeStretchSteps <= 5) {
            newPosition = 51 + homeStretchSteps
          } else {
            // Can't move - would overshoot
            setAnimatingPiece(null)
            setMovablePieces([])
            if (diceValue !== 6) {
              nextPlayer()
            } else {
              setDiceValue(null)
            }
            return
          }
        } else {
          newPosition = targetPosition
        }
      } else if (currentPosition >= 52) {
        // Already in home stretch
        newPosition = currentPosition + diceValue
        if (newPosition > 56) {
          // Can't move - would overshoot
          setAnimatingPiece(null)
          setMovablePieces([])
          if (diceValue !== 6) {
            nextPlayer()
          } else {
            setDiceValue(null)
          }
          return
        }
        if (newPosition === 56) {
          // Piece finished!
          newGameState[player].finished += 1
        }
      } else {
        // Normal movement on main path
        newPosition = targetPosition % 52
      }
    }

    // Check for captures (only on main path, not in home stretch)
    if (newPosition < 52 && !SAFE_POSITIONS.includes(newPosition)) {
      activePlayers.forEach(otherPlayer => {
        if (otherPlayer !== player) {
          newGameState[otherPlayer].pieces.forEach((pos, idx) => {
            if (pos === newPosition) {
              newGameState[otherPlayer].pieces[idx] = -1 // Send back home
            }
          })
        }
      })
    }

    newGameState[player].pieces[pieceIndex] = newPosition

    // Animate movement
    await new Promise(resolve => setTimeout(resolve, 500))

    setGameState(newGameState)
    setAnimatingPiece(null)
    setMovablePieces([])

    // Check for win
    if (newGameState[player].finished === 4) {
      setWinner(player)
      return
    }

    // Continue turn if rolled 6, otherwise next player
    if (diceValue !== 6) {
      nextPlayer()
    } else {
      setDiceValue(null)
    }
  }

  const nextPlayer = () => {
    setCurrentPlayerIndex((prev) => (prev + 1) % activePlayers.length)
    setDiceValue(null)
    setMovablePieces([])
  }

  const handlePieceClick = (player, pieceIndex) => {
    if (player !== currentPlayer || !movablePieces.includes(pieceIndex)) return

    movePiece(player, pieceIndex)
  }

  const getBoardPosition = (pathIndex, player = null) => {
    if (pathIndex < 0) return null
    if (pathIndex < 52) {
      // Regular board path
      return BOARD_PATH[pathIndex % 52]
    } else if (pathIndex < 57 && player) {
      // Home stretch path
      const homeStretchIndex = pathIndex - 52
      return HOME_STRETCH_PATHS[player][homeStretchIndex]
    }
    return null
  }

  const renderBoard = () => {
    const board = Array(15).fill(null).map(() => Array(15).fill(null))

    // Mark path positions
    BOARD_PATH.forEach((pos, index) => {
      const [row, col] = pos
      board[row][col] = { type: 'path', index }
    })

    // Mark home stretch paths
    Object.entries(HOME_STRETCH_PATHS).forEach(([color, positions]) => {
      positions.forEach(([row, col], index) => {
        board[row][col] = { type: 'home-stretch', color, index: index + 52 }
      })
    })

    // Mark safe positions
    SAFE_POSITIONS.forEach(index => {
      const pos = BOARD_PATH[index]
      if (pos) {
        const [row, col] = pos
        board[row][col] = { type: 'safe', index }
      }
    })

    // Mark home areas
    Object.entries(HOME_POSITIONS).forEach(([color, positions]) => {
      positions.forEach(([row, col]) => {
        board[row][col] = { type: 'home', color }
      })
    })

    // Mark starting positions
    Object.entries(START_POSITIONS).forEach(([color, pathIndex]) => {
      const pos = BOARD_PATH[pathIndex]
      if (pos) {
        const [row, col] = pos
        board[row][col] = { type: 'start', color, index: pathIndex }
      }
    })

    return board
  }

  const renderPieces = () => {
    const pieces = []

    activePlayers.forEach(player => {
      gameState[player].pieces.forEach((position, pieceIndex) => {
        let row, col

        if (position === -1) {
          // Piece is in home area
          const homePos = HOME_POSITIONS[player][pieceIndex]
          row = homePos[0]
          col = homePos[1]
        } else if (position === 56) {
          // Piece has finished - don't render
          return
        } else if (position >= 52) {
          // Piece is in home stretch
          const homeStretchPos = HOME_STRETCH_PATHS[player][position - 52]
          if (!homeStretchPos) return
          row = homeStretchPos[0]
          col = homeStretchPos[1]
        } else {
          // Piece is on main board
          const boardPos = getBoardPosition(position, player)
          if (!boardPos) return
          row = boardPos[0]
          col = boardPos[1]
        }

        const isMovable = movablePieces.includes(pieceIndex) && player === currentPlayer
        const isAnimating = animatingPiece?.player === player && animatingPiece?.piece === pieceIndex

        pieces.push(
          <div
            key={`${player}-${pieceIndex}`}
            className={`piece ${player} ${isMovable ? 'movable' : ''} ${isAnimating ? 'animating' : ''}`}
            style={{
              gridRow: row + 1,
              gridColumn: col + 1,
            }}
            onClick={() => handlePieceClick(player, pieceIndex)}
          />
        )
      })
    })

    return pieces
  }

  if (!gameStarted) {
    return (
      <div className="ludo-container">
        <h1>🎲 Ludo Game 🎲</h1>
        <div className="game-setup">
          <h2>Select Number of Players</h2>
          <div className="player-selection">
            {[2, 3, 4].map(num => (
              <button
                key={num}
                className={`player-btn ${numPlayers === num ? 'selected' : ''}`}
                onClick={() => setNumPlayers(num)}
              >
                {num} Players
              </button>
            ))}
          </div>
          <button className="start-btn" onClick={startGame}>
            Start Game
          </button>
        </div>
      </div>
    )
  }

  const board = renderBoard()

  return (
    <div className="ludo-container">
      <h1>🎲 Ludo Game 🎲</h1>

      {winner && (
        <div className="winner-announcement">
          <h2>🎉 {winner.toUpperCase()} WINS! 🎉</h2>
          <button onClick={startGame}>Play Again</button>
        </div>
      )}

      <div className="game-info">
        <div className="current-player">
          Current Player: <span className={`player-name ${currentPlayer}`}>{currentPlayer.toUpperCase()}</span>
        </div>

        <div className="dice-section">
          <div className={`dice ${isRolling ? 'rolling' : ''}`}>
            {diceValue || '?'}
          </div>
          <button
            className="dice-btn"
            onClick={rollDice}
            disabled={isRolling || winner || (diceValue && movablePieces.length > 1)}
          >
            {isRolling ? 'Rolling...' : 'Roll Dice'}
          </button>
        </div>
      </div>

      <div className="board-container">
        <div className="board">
          {board.map((row, rowIndex) =>
            row.map((cell, colIndex) => (
              <div
                key={`${rowIndex}-${colIndex}`}
                className={`cell ${cell?.type || ''} ${cell?.color || ''}`}
              />
            ))
          )}
          {renderPieces()}
        </div>
      </div>

      <div className="game-controls">
        <button onClick={() => setGameStarted(false)}>New Game</button>
      </div>
    </div>
  )
}

export default Ludo
