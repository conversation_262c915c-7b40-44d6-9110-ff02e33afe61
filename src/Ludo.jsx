import { useState } from 'react'
import './Ludo.css'  // We'll style the grid here

function Ludo() {
  const players = ['red', 'green', 'yellow', 'blue']
  const [activePlayers, setActivePlayers] = useState(players.slice(0, 2)) // default 2-player
  const [positions, setPositions] = useState({
    red: [0, 0, 0, 0],
    green: [0, 0, 0, 0],
    yellow: [0, 0, 0, 0],
    blue: [0, 0, 0, 0],
  })
  const [currentPlayer, setCurrentPlayer] = useState('red')
  const [diceRoll, setDiceRoll] = useState(null)

  const rollDice = () => {
    const roll = Math.floor(Math.random() * 6) + 1
    setDiceRoll(roll)

    // Placeholder: Move first available token for now
    const newPositions = { ...positions }
    const tokenIndex = 0 // We can improve logic to choose token
    newPositions[currentPlayer][tokenIndex] += roll
    setPositions(newPositions)

    // Switch to next player
    const nextIndex = (activePlayers.indexOf(currentPlayer) + 1) % activePlayers.length
    setCurrentPlayer(activePlayers[nextIndex])
  }

  return (
    <div className="ludo-container">
      <h1>Ludo</h1>
      <p>Current Player: {currentPlayer.toUpperCase()}</p>
      <p>Dice Roll: {diceRoll !== null ? diceRoll : '-'}</p>
      <button onClick={rollDice}>Roll Dice</button>

      <div className="board">
        {/* Render grid squares and tokens here */}
        {/* Example: 15x15 grid */}
        {[...Array(15 * 15)].map((_, idx) => (
          <div key={idx} className="cell"></div>
        ))}
      </div>
    </div>
  )
}

export default Ludo
